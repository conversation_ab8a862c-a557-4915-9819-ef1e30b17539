FROM docker-group.iad.ca.inet:8473/intact/ocp-openjdk21-base:1@sha256:a2355d990fe098b781a77c6169f14846b41a8c59cfa8abcaee8c07e3020c5f5a

ARG LABEL_VERSION
LABEL version=$LABEL_VERSION

ARG APPLICATION='autoquote-backend'
ENV APPLICATION=$APPLICATION

LABEL OWNER_TEAM="SE_INTACT_LAB" \
      TRIBE_TEAM="acquisition" \
      SQUAD_TEAM="AEGIS"

USER root

ENV APP_BASE="/opt/${APPLICATION}" \
    JAVA_OPTS="-Xms256m -Xmx512m -Duser.timezone=America/Montreal \
    --add-opens java.base/java.lang=ALL-UNNAMED \
    --add-opens java.base/java.util=ALL-UNNAMED \
    -Dcom.atomikos.icatch.default_jta_timeout=100000 \
    -Dcom.atomikos.icatch.max_timeout=900000 \
    -Dcom.atomikos.icatch.max_actives=200 \
    -Dcom.atomikos.icatch.force_shutdown_on_vm_exit=true \
    -Dspring.config.additional-location=file:/mnt/properties/ \
    " \

    USER_NAME="IRCA"

EXPOSE 8080

RUN mkdir -p ${APP_BASE} \
    && mkdir -p /atomikos/log/ \
    && groupadd --gid 5000 ${USER_NAME} \
    && useradd --home-dir /home/<USER>/bin/sh --skel /dev/null ${USER_NAME} \
    && chown -R ${USER_NAME}:${USER_NAME} /atomikos \
    && chmod 777 -R /atomikos

COPY autoquote-backend-web/target/autoquote-backend-web.jar ${APP_BASE}/${APPLICATION}.jar

RUN chmod -R g+w ${APP_BASE}

USER ${USER_NAME}

ENTRYPOINT ${INSTALL_BASE_DIR}/bin/start-openjdk.sh && \
           java ${JAVA_OPTS} -cp /mnt/properties/:${APP_BASE}/${APPLICATION}.jar org.springframework.boot.loader.JarLauncher
